/**
 * 任务队列版自动填写工具
 */

(function() {
    // 核心函数
    function getElementByXPath(xpath, doc) {
        if (!doc) doc = document;
        return doc.evaluate(xpath, doc, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null).singleNodeValue;
    }

    // 查找所有可能的文档上下文（包括iframe）
    function getAllDocuments() {
        var docs = [document];
        
        var iframes = document.querySelectorAll('iframe');
        for (var i = 0; i < iframes.length; i++) {
            try {
                if (iframes[i].contentDocument) {
                    docs.push(iframes[i].contentDocument);
                }
            } catch (e) {
                // 跨域iframe无法访问
            }
        }
        
        if (window !== window.top) {
            try {
                docs.push(window.top.document);
            } catch (e) {
                // 跨域无法访问
            }
        }
        
        return docs;
    }

    // 在所有文档中查找元素
    function findElementInAllDocs(xpath) {
        var docs = getAllDocuments();
        
        for (var i = 0; i < docs.length; i++) {
            try {
                var element = getElementByXPath(xpath, docs[i]);
                if (element) {
                    return { element: element, document: docs[i] };
                }
            } catch (e) {
                // 继续查找下一个文档
            }
        }
        
        return null;
    }

    // 填写输入框函数
    function fillInput(inputXPath, inputValue) {
        console.log('填写输入框');
        var inputResult = findElementInAllDocs(inputXPath);

        if (inputResult) {
            var input = inputResult.element;
            input.value = inputValue;
            input.dispatchEvent(new Event('input', { bubbles: true }));
            input.dispatchEvent(new Event('change', { bubbles: true }));
            input.dispatchEvent(new Event('blur', { bubbles: true }));
            console.log('输入框填写完成，值: "' + inputValue + '"');
            return true;
        } else {
            console.log('未找到输入框: ' + inputXPath);
            return false;
        }
    }

    // 点击元素函数
    function clickElement(elementXPath, description) {
        console.log(description || '点击元素');
        var elementResult = findElementInAllDocs(elementXPath);

        if (elementResult) {
            var element = elementResult.element;
            element.click();
            console.log('点击完成: ' + (description || elementXPath));
            return true;
        } else {
            console.log('未找到元素: ' + elementXPath);
            return false;
        }
    }



    // 通过XPath查找多个相同路径的元素并按索引填写
    function fillInputByXPathIndex(xpath, index, inputValue, description) {
        console.log(description || '通过XPath索引填写输入框');
        var docs = getAllDocuments();

        for (var i = 0; i < docs.length; i++) {
            try {
                var result = docs[i].evaluate(xpath, docs[i], null, XPathResult.ORDERED_NODE_SNAPSHOT_TYPE, null);
                
                if (result.snapshotLength > index) {
                    var element = result.snapshotItem(index);
                    element.value = inputValue;
                    element.dispatchEvent(new Event('input', { bubbles: true }));
                    element.dispatchEvent(new Event('change', { bubbles: true }));
                    element.dispatchEvent(new Event('blur', { bubbles: true }));
                    console.log('输入框填写完成，XPath索引: ' + index + ', 值: "' + inputValue + '"');
                    return true;
                }
            } catch (e) {
                // 继续查找下一个文档
            }
        }

        console.log('未找到XPath "' + xpath + '" 索引为 ' + index + ' 的输入框');
        return false;
    }

    // 任务队列执行函数
    function executeTasksWithDelay(tasks, delay) {
        var currentIndex = 0;
        
        function executeNextTask() {
            if (currentIndex < tasks.length) {
                console.log('执行任务 ' + (currentIndex + 1) + '/' + tasks.length);
                tasks[currentIndex]();
                currentIndex++;
                setTimeout(executeNextTask, delay);
            } else {
                console.log('🎉 所有任务执行完成！');
            }
        }
        
        executeNextTask();
    }

    // 立即执行自动填写
    console.log('🚀 开始任务队列自动填写...');

    // 定义所有任务
    var tasks = [
        // 任务1: 检查并点击单选框
        function() {
            var radioXPath = "/html/body/div[9]/div[2]/div/div[2]/div/div/div/div[2]/div[6]/div[2]/div/span/span[1]";
            var radioResult = findElementInAllDocs(radioXPath);
            if (radioResult) {
                var radioElement = radioResult.element;
                var className = radioElement.className;
                if (className.indexOf('checked') !== -1) {
                    console.log('单选框已经选中，跳过点击');
                } else {
                    console.log('单选框未选中，进行点击');
                    clickElement(radioXPath, "是否跨区（默认不跨区）");
                }
            } else {
                console.log('未找到单选框元素');
            }
        },

        // 任务2: 第一次点击下拉框
        function() {
            var dropdown1XPath = "/html/body/div[9]/div[2]/div/div[2]/div/div/div/div[4]/div[6]/span/span/span[1]/span[3]";
            clickElement(dropdown1XPath, "下拉框第一次点击");
        },

        // 任务3: 第二次点击下拉框
        function() {
            var dropdown2XPath = "/html/body/div[10]/div/span[2]";
            clickElement(dropdown2XPath, "下拉框第二次点击投资类型选项为企业投资");
        },

        // 任务4: 填写电子监管号1
        function() {
            var inputXPath = "/html/body/div[9]/div[3]/div/div[2]/div/div/div/div[1]/div[4]/span/span/span[1]/span[2]/div/input";
            fillInput(inputXPath, "/");
        },

        // 任务5: 填写电子监管号2
        function() {
            var inputXPath = "/html/body/div[9]/div[3]/div/div[2]/div/div/div/div[1]/div[6]/span/span/span[1]/span[2]/div/input";
            fillInput(inputXPath, "/");
        },

        // 任务6: 填写专门部门意见1
        function() {
            var inputXPath = "/html/body/div[9]/div[8]/div/div[2]/div/div/div[2]/fieldset/div/div[3]/div/div/div/div[3]/div/div[2]/div/div[2]/span[2]/span";
            fillInput(inputXPath, "核查两重点一重大危化品防控范围等其他专业部");
        },

        // 任务7: 填写专门部门意见2
        function() {
            var inputXPath = "/html/body/div[9]/div[8]/div/div[2]/div/div/div[2]/fieldset/div/div[3]/div/div/div/div[3]/div/div[2]/div/div[2]/span[4]/span";
            fillInput(inputXPath, "不涉及");
        },

        // 任务8: 填写第一个组件输入框
        function() {
            var componentXPath = "/html/body/div[9]/div[8]/div/div[2]/div/div/div[2]/fieldset/div/div[3]/div/div/div/div[3]/span/span/span[1]/span[2]/div/input";
            fillInputByXPathIndex(componentXPath, 0, "第一个输入框的值", "填写第一个组件输入框（XPath索引0）");
        },

        // 任务9: 填写第二个组件输入框
        function() {
            var componentXPath = "/html/body/div[9]/div[8]/div/div[2]/div/div/div[2]/fieldset/div/div[3]/div/div/div/div[3]/span/span/span[1]/span[2]/div/input";
            fillInputByXPathIndex(componentXPath, 1, "第二个输入框的值", "填写第二个组件输入框（XPath索引1）");
        },

        // 任务10: 点击保存按钮
        function() {
            var saveButtonXPath = "/html/body/div[3]/div/div[4]/div/div/div[2]/div/div[1]/div[1]/div[1]/button";
            clickElement(saveButtonXPath, "点击保存按钮");
        }
    ];

    // 执行任务队列，每个任务间隔500ms
    executeTasksWithDelay(tasks, 500);

})();
