/**
 * 超级简化版本 - 直接使用完整XPath
 * 最小化代码，专注于核心功能
 */

(function() {
    console.log('🚀 开始超级简化版自动填写...');
    
    // 完整XPath
    const inputXPath = "/html/body/div[9]/div[3]/div/div[2]/div/div/div/div[1]/div[4]/span/span/span[1]/span[2]/div/input";
    
    function getElementByXPath(xpath) {
        const result = document.evaluate(xpath, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null);
        return result.singleNodeValue;
    }
    
    function wait(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    
    async function autoFill() {
        try {
            // 1. 查找输入框
            console.log('🔍 查找输入框...');
            const input = getElementByXPath(inputXPath);
            
            if (!input) {
                console.error('❌ 未找到输入框');
                console.log('📋 页面上的所有输入框:');
                document.querySelectorAll('input').forEach((inp, i) => {
                    console.log(`${i+1}. ID: "${inp.id}", Type: "${inp.type}", Class: "${inp.className}"`);
                });
                return false;
            }
            
            console.log('✅ 找到输入框:', input);
            
            // 2. 填写输入框
            console.log('✏️ 填写输入框...');
            input.focus();
            input.value = '';
            input.value = '张三';
            
            // 触发事件
            input.dispatchEvent(new Event('input', { bubbles: true }));
            input.dispatchEvent(new Event('change', { bubbles: true }));
            input.dispatchEvent(new Event('blur', { bubbles: true }));
            
            console.log('✅ 输入完成，当前值:', input.value);
            
            await wait(500);
            
            // 3. 查找保存按钮
            console.log('🔍 查找保存按钮...');
            let saveBtn = null;
            
            // 查找所有可能的保存按钮
            const buttons = document.querySelectorAll('button, input[type="button"], input[type="submit"]');
            for (const btn of buttons) {
                const text = (btn.textContent || btn.value || '').trim();
                if (text.includes('保存')) {
                    saveBtn = btn;
                    console.log('✅ 找到保存按钮:', text);
                    break;
                }
            }
            
            if (!saveBtn) {
                console.error('❌ 未找到保存按钮');
                console.log('📋 页面上的所有按钮:');
                buttons.forEach((btn, i) => {
                    const text = (btn.textContent || btn.value || '').trim();
                    console.log(`${i+1}. Text: "${text}", ID: "${btn.id}", Class: "${btn.className}"`);
                });
                return false;
            }
            
            // 4. 点击保存按钮
            console.log('🖱️ 点击保存按钮...');
            saveBtn.scrollIntoView({ behavior: 'smooth', block: 'center' });
            await wait(300);
            
            saveBtn.dispatchEvent(new MouseEvent('click', { 
                bubbles: true, 
                cancelable: true, 
                view: window 
            }));
            
            console.log('✅ 保存按钮已点击');
            
            await wait(1000);
            
            // 5. 验证结果
            console.log('🔍 验证结果...');
            const finalValue = input.value;
            console.log('📄 最终输入框值:', finalValue);
            
            if (finalValue === '张三') {
                console.log('🎉 自动填写成功！');
                return true;
            } else {
                console.log('⚠️ 值可能未保存，请检查');
                return false;
            }
            
        } catch (error) {
            console.error('❌ 错误:', error);
            return false;
        }
    }
    
    // 执行
    autoFill().then(success => {
        if (success) {
            console.log('🎊 任务完成！');
        } else {
            console.log('🔧 需要调试');
        }
    });
    
    // 导出供手动调用
    window.ultraSimpleAutoFill = autoFill;
    
})();
