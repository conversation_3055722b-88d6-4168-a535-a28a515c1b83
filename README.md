# 自动填写表单工具

这是一个用于在Chrome浏览器控制台中自动填写表单的JavaScript工具，专门针对 `yth.gzlpc.gov.cn` 系统。

## 文件说明

- `simple-xpath.js` - 简洁版自动填写工具
- `README.md` - 使用说明文档

## 使用方法

1. 打开目标网页：`https://yth.gzlpc.gov.cn/sp/#!/approval/handlingBox`
2. 点击相应按钮，确保输入框处于可操作状态
3. 按 F12 打开开发者工具
4. 切换到 Console（控制台）标签
5. 复制 `simple-xpath.js` 文件的全部内容
6. 粘贴到控制台并按回车执行
7. 自动完成填写"张三"并保存

## 工具特性

- 基于验证的工作代码构建
- 使用精确的XPath定位
- 自动填写并保存
- 简洁高效，只有54行代码
- 立即执行，无需手动调用函数

## 核心代码逻辑

```javascript
// 1. 使用XPath查找输入框
var input = getElementByXPath("/html/body/div[9]/div[3]/div/div[2]/div/div/div/div[1]/div[4]/span/span/span[1]/span[2]/div/input");

// 2. 填写值并触发事件
if (input) {
    input.value = "张三";
    input.dispatchEvent(new Event('input', { bubbles: true }));
}

// 3. 自动查找并点击保存按钮
```

## 工具特性

### 完整版工具特性：
- 多种元素查找策略（CSS选择器 + XPath）
- 模拟真实用户输入（逐字符输入）
- 智能等待元素加载
- 完整的事件触发序列
- 详细的日志输出
- 调试模式

### 简化版工具特性：
- 简单直接的元素查找
- 基本的事件触发
- 调试信息输出
- 容错处理

## 常见问题解决

### 问题1：找不到输入框
**解决方案：**
1. 运行 `debugPageElements()` 查看页面所有输入框
2. 检查输入框的ID、Class等属性
3. 如果需要，可以修改代码中的选择器

### 问题2：保存按钮点击无效
**解决方案：**
1. 确认保存按钮的文本内容确实包含"保存"
2. 检查是否有其他同名按钮干扰
3. 尝试手动点击一次保存按钮，观察网络请求

### 问题3：输入的值没有保存
**可能原因：**
1. 表单验证失败
2. 需要触发特定的事件
3. 页面使用了特殊的框架（如React、Vue等）

**解决方案：**
1. 检查浏览器控制台是否有错误信息
2. 观察网络请求是否正常发送
3. 尝试手动输入一次，对比事件触发的差异

## 自定义修改

如果默认的选择器不工作，你可以：

1. 使用 `debugPageElements()` 找到正确的元素属性
2. 修改代码中的选择器数组
3. 添加新的XPath表达式

### 修改输入框选择器示例：
```javascript
// 在 findInputElement 函数中添加新的选择器
const selectors = [
    "input#你找到的ID",
    "input.你找到的Class",
    // ... 其他选择器
];
```

### 修改保存按钮选择器示例：
```javascript
// 在 findSaveButton 函数中添加新的XPath
const xpaths = [
    "//button[@id='你找到的ID']",
    "//button[contains(@class, '你找到的Class')]",
    // ... 其他XPath
];
```

## 测试步骤

1. 首先运行调试命令查看页面元素
2. 确认能找到目标输入框和保存按钮
3. 运行自动填写工具
4. 观察控制台输出的日志信息
5. 检查页面是否成功填写和保存

## 注意事项

- 确保页面完全加载后再运行脚本
- 如果页面使用了iframe，可能需要切换到对应的frame
- 某些网站可能有反自动化机制，需要适当调整延迟时间
- 建议先在测试环境中验证脚本的有效性

## 联系支持

如果遇到问题，请提供：
1. 浏览器控制台的错误信息
2. `debugPageElements()` 的输出结果
3. 页面的具体URL和操作步骤
