# 自动填写表单工具

这是一个用于在Chrome浏览器控制台中自动填写表单的JavaScript工具，专门针对 `yth.gzlpc.gov.cn` 系统。

## 文件说明

- `simple-xpath.js` - 简洁版自动填写工具
- `README.md` - 使用说明文档

## 使用方法

1. 打开目标网页：`https://yth.gzlpc.gov.cn/sp/#!/approval/handlingBox`
2. 点击相应按钮，确保输入框处于可操作状态
3. 按 F12 打开开发者工具
4. 切换到 Console（控制台）标签
5. 复制 `simple-xpath.js` 文件的全部内容
6. 粘贴到控制台并按回车执行
7. 自动完成填写"张三"并保存

## 工具特性

- 基于验证的工作代码构建
- 使用精确的XPath定位
- 自动填写并保存
- 简洁高效，只有54行代码
- 立即执行，无需手动调用函数

## 核心代码逻辑

```javascript
// 1. 使用XPath查找输入框
var input = getElementByXPath("/html/body/div[9]/div[3]/div/div[2]/div/div/div/div[1]/div[4]/span/span/span[1]/span[2]/div/input");

// 2. 填写值并触发事件
if (input) {
    input.value = "张三";
    input.dispatchEvent(new Event('input', { bubbles: true }));
}

// 3. 自动查找并点击保存按钮
```

## 注意事项

- 确保在点击按钮后，输入框处于可操作状态时运行脚本
- 脚本会自动填写"张三"并尝试保存
- 如果找不到保存按钮，会提示手动点击保存
- 基于用户验证的工作代码，可靠性高

## 故障排除

如果脚本不工作：
1. 确认已点击相应按钮，输入框可见且可操作
2. 检查控制台输出的错误信息
3. 确认页面URL和结构没有变化
