/**
 * 快速测试版本 - 使用确认的XPath
 * 专门针对 //*[@id="xui.UI.ComboInput-INPUT:ac:"] 这个输入框
 */

(function() {
    console.log('🚀 开始快速测试...');
    
    // 使用确认的XPath
    const correctXPath = "//*[@id=\"xui.UI.ComboInput-INPUT:ac:\"]";
    
    function findElementByXPath(xpath) {
        try {
            const result = document.evaluate(xpath, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null);
            return result.singleNodeValue;
        } catch (e) {
            console.error('XPath查找失败:', e);
            return null;
        }
    }
    
    function findSaveButton() {
        // 查找保存按钮
        const buttons = Array.from(document.querySelectorAll('button, input[type="button"], input[type="submit"], a, span, div'));
        
        for (const btn of buttons) {
            const text = (btn.textContent || btn.value || btn.innerText || '').trim();
            if (text.includes('保存') && btn.offsetParent !== null) {
                console.log(`✅ 找到保存按钮: "${text}"`);
                return btn;
            }
        }
        
        // 尝试XPath查找保存按钮
        const saveXPaths = [
            "//button[contains(text(), '保存')]",
            "//input[@type='button' and contains(@value, '保存')]",
            "//input[@type='submit' and contains(@value, '保存')]",
            "//*[contains(text(), '保存')]"
        ];
        
        for (const xpath of saveXPaths) {
            const element = findElementByXPath(xpath);
            if (element && element.offsetParent !== null) {
                console.log(`✅ 通过XPath找到保存按钮: ${xpath}`);
                return element;
            }
        }
        
        return null;
    }
    
    async function wait(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    
    async function fillAndSave(inputValue = "张三") {
        try {
            // 1. 查找输入框
            console.log(`🔍 使用XPath查找输入框: ${correctXPath}`);
            const inputElement = findElementByXPath(correctXPath);
            
            if (!inputElement) {
                console.error('❌ 未找到输入框');
                console.log('💡 让我们检查页面上的所有输入框...');
                
                // 显示所有输入框
                const allInputs = document.querySelectorAll('input');
                console.log(`📋 页面上共有 ${allInputs.length} 个输入框:`);
                allInputs.forEach((input, index) => {
                    console.log(`${index + 1}. ID: "${input.id}", Type: "${input.type}", Name: "${input.name}", Class: "${input.className}"`);
                });
                
                return false;
            }
            
            console.log('✅ 找到输入框!');
            console.log('📝 输入框信息:', {
                id: inputElement.id,
                type: inputElement.type,
                name: inputElement.name,
                className: inputElement.className,
                value: inputElement.value
            });
            
            // 2. 填写输入框
            console.log(`✏️ 正在填写值: "${inputValue}"`);
            
            // 聚焦
            inputElement.focus();
            await wait(100);
            
            // 清空并设置值
            inputElement.value = '';
            inputElement.value = inputValue;
            
            // 触发事件
            const events = [
                new Event('focus', { bubbles: true }),
                new Event('input', { bubbles: true }),
                new Event('change', { bubbles: true }),
                new KeyboardEvent('keydown', { bubbles: true }),
                new KeyboardEvent('keyup', { bubbles: true }),
                new Event('blur', { bubbles: true })
            ];
            
            for (const event of events) {
                inputElement.dispatchEvent(event);
                await wait(50);
            }
            
            console.log('✅ 输入框填写完成');
            console.log('📄 当前输入框值:', inputElement.value);
            
            // 3. 等待一下
            await wait(500);
            
            // 4. 查找并点击保存按钮
            console.log('🔍 正在查找保存按钮...');
            const saveButton = findSaveButton();
            
            if (!saveButton) {
                console.error('❌ 未找到保存按钮');
                console.log('💡 让我们检查页面上的所有按钮...');
                
                // 显示所有按钮
                const allButtons = document.querySelectorAll('button, input[type="button"], input[type="submit"], a');
                console.log(`📋 页面上共有 ${allButtons.length} 个按钮:`);
                allButtons.forEach((btn, index) => {
                    const text = (btn.textContent || btn.value || btn.innerText || '').trim();
                    console.log(`${index + 1}. Text: "${text}", ID: "${btn.id}", Class: "${btn.className}", Tag: "${btn.tagName}"`);
                });
                
                return false;
            }
            
            console.log('✅ 找到保存按钮!');
            console.log('🔘 保存按钮信息:', {
                text: saveButton.textContent || saveButton.value,
                id: saveButton.id,
                className: saveButton.className,
                tagName: saveButton.tagName
            });
            
            // 5. 点击保存按钮
            console.log('🖱️ 正在点击保存按钮...');
            
            // 滚动到按钮位置
            saveButton.scrollIntoView({ behavior: 'smooth', block: 'center' });
            await wait(300);
            
            // 触发点击事件
            const clickEvents = [
                new MouseEvent('mousedown', { bubbles: true, cancelable: true, view: window }),
                new MouseEvent('mouseup', { bubbles: true, cancelable: true, view: window }),
                new MouseEvent('click', { bubbles: true, cancelable: true, view: window })
            ];
            
            for (const event of clickEvents) {
                saveButton.dispatchEvent(event);
                await wait(50);
            }
            
            console.log('✅ 保存按钮点击完成');
            
            // 6. 等待保存完成
            await wait(1000);
            
            // 7. 验证结果
            console.log('🔍 验证填写结果...');
            const finalValue = inputElement.value;
            console.log(`📄 最终输入框值: "${finalValue}"`);
            
            if (finalValue === inputValue) {
                console.log('🎉 自动填写成功！');
                return true;
            } else {
                console.log('⚠️ 输入框值可能未保存，请检查页面状态');
                return false;
            }
            
        } catch (error) {
            console.error('❌ 执行过程中出错:', error);
            return false;
        }
    }
    
    // 立即执行
    fillAndSave("张三").then(success => {
        if (success) {
            console.log('🎊 任务完成！');
        } else {
            console.log('🔧 需要进一步调试');
        }
    });
    
    // 导出函数供手动调用
    window.quickTest = fillAndSave;
    
})();
