/**
 * 简洁版自动填写工具
 */

(function() {
    // 核心函数
    function getElementByXPath(xpath) {
        return document.evaluate(xpath, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null).singleNodeValue;
    }

    // 立即执行自动填写
    console.log('🚀 开始自动填写...');

    // 填写输入框
    var inputXPath = "/html/body/div[9]/div[3]/div/div[2]/div/div/div/div[1]/div[4]/span/span/span[1]/span[2]/div/input";
    var input = getElementByXPath(inputXPath);

    if (input) {
        console.log('✅ 找到输入框');
        input.value = "张三";
        input.dispatchEvent(new Event('input', { bubbles: true }));
        console.log('✅ 输入框填写完成');

        // 等待500ms后点击保存按钮
        setTimeout(function() {
            console.log('🔍 查找保存按钮...');

            var buttons = document.querySelectorAll('button, input[type="button"], input[type="submit"]');
            var saveButton = null;

            for (var i = 0; i < buttons.length; i++) {
                var btn = buttons[i];
                var text = (btn.textContent || btn.value || '').trim();

                if (text === '保存' && btn.offsetParent !== null) {
                    saveButton = btn;
                    break;
                }
            }

            if (saveButton) {
                console.log('✅ 找到保存按钮，正在点击...');
                saveButton.click();
                console.log('🎉 自动填写和保存完成！');
            } else {
                console.log('⚠️ 未找到保存按钮，请手动点击保存');
            }
        }, 500);

    } else {
        console.error('❌ 未找到输入框，请确保已点击按钮，输入框处于可操作状态');
    }

})();
