/**
 * 简化版XPath自动填写工具 - 兼容性更好
 */

(function() {
    console.log('🚀 XPath自动填写工具已加载');
    
    // 工具函数
    function getElementByXPath(xpath) {
        try {
            var result = document.evaluate(xpath, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null);
            return result.singleNodeValue;
        } catch (e) {
            console.error('XPath查找失败:', e);
            return null;
        }
    }
    
    function wait(ms) {
        return new Promise(function(resolve) {
            setTimeout(resolve, ms);
        });
    }
    
    // 简单的XPath生成器
    function generateSimpleXPath(element) {
        if (!element) return '';
        
        if (element.id) {
            return '//*[@id="' + element.id + '"]';
        }
        
        var path = '';
        var current = element;
        
        while (current && current.nodeType === 1) {
            var tagName = current.tagName.toLowerCase();
            var index = 1;
            
            // 计算同级同标签元素的索引
            var sibling = current.previousSibling;
            while (sibling) {
                if (sibling.nodeType === 1 && sibling.tagName === current.tagName) {
                    index++;
                }
                sibling = sibling.previousSibling;
            }
            
            path = '/' + tagName + '[' + index + ']' + path;
            current = current.parentNode;
            
            if (current === document.body) {
                path = '/html/body' + path;
                break;
            }
        }
        
        return path;
    }
    
    // 调试函数
    function debug() {
        console.log('=== 输入框信息 ===');
        var inputs = document.querySelectorAll('input');
        
        for (var i = 0; i < inputs.length; i++) {
            var inp = inputs[i];
            var xpath = generateSimpleXPath(inp);
            var rect = inp.getBoundingClientRect();
            var isVisible = inp.offsetParent !== null;
            
            console.log((i + 1) + '. Type: "' + inp.type + '", 可见: ' + isVisible);
            console.log('    XPath: ' + xpath);
            console.log('    位置: x=' + Math.round(rect.x) + ', y=' + Math.round(rect.y));
            console.log('    Class: "' + inp.className + '"');
            console.log('    ---');
        }
        
        console.log('\n=== 保存按钮信息 ===');
        var buttons = document.querySelectorAll('button, input[type="button"], input[type="submit"]');
        
        for (var i = 0; i < buttons.length; i++) {
            var btn = buttons[i];
            var text = (btn.textContent || btn.value || '').trim();
            
            if (text.indexOf('保存') !== -1) {
                var xpath = generateSimpleXPath(btn);
                console.log((i + 1) + '. Text: "' + text + '"');
                console.log('    XPath: ' + xpath);
                console.log('    Class: "' + btn.className + '"');
                console.log('    ---');
            }
        }
    }
    
    // 使用XPath填写输入框
    function fillByXPath(xpath, value) {
        if (!value) value = "张三";
        
        console.log('🔍 使用XPath查找输入框: ' + xpath);
        
        var input = getElementByXPath(xpath);
        if (!input) {
            console.error('❌ 未找到输入框');
            return false;
        }
        
        console.log('✅ 找到输入框');
        console.log('类型: ' + input.type + ', 当前值: "' + input.value + '"');
        
        // 填写输入框
        input.focus();
        input.value = '';
        input.value = value;
        
        // 触发事件
        input.dispatchEvent(new Event('input', { bubbles: true }));
        input.dispatchEvent(new Event('change', { bubbles: true }));
        input.dispatchEvent(new Event('blur', { bubbles: true }));
        
        console.log('✅ 填写完成，当前值: "' + input.value + '"');
        return true;
    }
    
    // 使用XPath点击保存按钮
    function clickSaveByXPath(xpath) {
        console.log('🔍 使用XPath查找保存按钮: ' + xpath);
        
        var button = getElementByXPath(xpath);
        if (!button) {
            console.error('❌ 未找到保存按钮');
            return false;
        }
        
        console.log('✅ 找到保存按钮');
        console.log('文本: "' + (button.textContent || button.value) + '"');
        
        // 点击按钮
        button.scrollIntoView({ behavior: 'smooth', block: 'center' });
        button.dispatchEvent(new MouseEvent('click', {
            bubbles: true,
            cancelable: true,
            view: window
        }));
        
        console.log('✅ 保存按钮已点击');
        return true;
    }
    
    // 完整流程
    function autoFillComplete(inputXPath, saveButtonXPath, inputValue) {
        if (!inputValue) inputValue = "张三";
        
        console.log('🚀 开始完整自动填写流程...');
        
        return wait(1000).then(function() {
            // 1. 填写输入框
            var fillSuccess = fillByXPath(inputXPath, inputValue);
            if (!fillSuccess) {
                return Promise.resolve(false);
            }
            
            return wait(500);
        }).then(function() {
            // 2. 点击保存按钮
            var clickSuccess = clickSaveByXPath(saveButtonXPath);
            if (!clickSuccess) {
                return Promise.resolve(false);
            }
            
            return wait(1000);
        }).then(function() {
            // 3. 验证结果
            var input = getElementByXPath(inputXPath);
            if (input && input.value === inputValue) {
                console.log('🎉 自动填写成功！');
                return true;
            } else {
                console.log('⚠️ 值可能未保存，请检查页面状态');
                return false;
            }
        }).catch(function(error) {
            console.error('❌ 错误:', error);
            return false;
        });
    }
    
    // 使用说明
    console.log('💡 使用方法:');
    console.log('1. debug() - 查看所有输入框和按钮的XPath');
    console.log('2. fillByXPath("XPath", "值") - 填写指定输入框');
    console.log('3. clickSaveByXPath("XPath") - 点击保存按钮');
    console.log('4. autoFillComplete("输入框XPath", "保存按钮XPath", "值") - 完整流程');
    
    // 导出函数
    window.debug = debug;
    window.fillByXPath = fillByXPath;
    window.clickSaveByXPath = clickSaveByXPath;
    window.autoFillComplete = autoFillComplete;
    
})();
