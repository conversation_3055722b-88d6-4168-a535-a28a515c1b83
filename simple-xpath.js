/**
 * 跨iframe自动填写工具
 */

(function() {
    // 核心函数
    function getElementByXPath(xpath, doc) {
        if (!doc) doc = document;
        return doc.evaluate(xpath, doc, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null).singleNodeValue;
    }

    // 查找所有可能的文档上下文（包括iframe）
    function getAllDocuments() {
        var docs = [document];

        // 查找所有iframe
        var iframes = document.querySelectorAll('iframe');
        for (var i = 0; i < iframes.length; i++) {
            try {
                if (iframes[i].contentDocument) {
                    docs.push(iframes[i].contentDocument);
                }
            } catch (e) {
                // 跨域iframe无法访问
            }
        }

        // 检查是否在iframe中，如果是，也包含顶级文档
        if (window !== window.top) {
            try {
                docs.push(window.top.document);
            } catch (e) {
                // 跨域无法访问
            }
        }

        return docs;
    }

    // 在所有文档中查找元素
    function findElementInAllDocs(xpath) {
        var docs = getAllDocuments();

        for (var i = 0; i < docs.length; i++) {
            try {
                var element = getElementByXPath(xpath, docs[i]);
                if (element) {
                    console.log('在文档' + (i + 1) + '中找到元素');
                    return { element: element, document: docs[i] };
                }
            } catch (e) {
                // 继续查找下一个文档
            }
        }

        return null;
    }

    // 立即执行自动填写
    console.log('🚀 开始跨iframe自动填写...');

    // 填写输入框
    var inputXPath = "/html/body/div[9]/div[3]/div/div[2]/div/div/div/div[1]/div[4]/span/span/span[1]/span[2]/div/input";
    var inputResult = findElementInAllDocs(inputXPath);

    if (inputResult) {
        var input = inputResult.element;
        console.log('✅ 找到输入框');
        input.value = "张三";
        input.dispatchEvent(new Event('input', { bubbles: true }));
        input.dispatchEvent(new Event('change', { bubbles: true }));
        input.dispatchEvent(new Event('blur', { bubbles: true }));
        console.log('✅ 输入框填写完成');

        // 等待1秒后点击保存按钮
        setTimeout(function() {
            console.log('🔍 在所有文档中查找保存按钮...');

            var saveButtonXPath = "/html/body/div[3]/div/div[4]/div/div/div[2]/div/div[1]/div[1]/div[1]/button";
            var saveResult = findElementInAllDocs(saveButtonXPath);

            if (saveResult) {
                var saveButton = saveResult.element;
                console.log('✅ 找到保存按钮，正在点击...');
                saveButton.click();
                console.log('🎉 自动填写和保存完成！');
            } else {
                console.log('⚠️ 未找到保存按钮');
                console.log('💡 尝试手动运行保存代码：');
                console.log('window.top.document.evaluate("/html/body/div[3]/div/div[4]/div/div/div[2]/div/div[1]/div[1]/div[1]/button", window.top.document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null).singleNodeValue.click()');
            }
        }, 1000);

    } else {
        console.error('❌ 未找到输入框，请确保已点击按钮，输入框处于可操作状态');
    }

})();
