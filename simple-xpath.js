/**
 * 跨iframe自动填写工具
 */

(function() {
    // 核心函数
    function getElementByXPath(xpath, doc) {
        if (!doc) doc = document;
        return doc.evaluate(xpath, doc, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null).singleNodeValue;
    }

    // 查找所有可能的文档上下文（包括iframe）
    function getAllDocuments() {
        var docs = [document];

        // 查找所有iframe
        var iframes = document.querySelectorAll('iframe');
        for (var i = 0; i < iframes.length; i++) {
            try {
                if (iframes[i].contentDocument) {
                    docs.push(iframes[i].contentDocument);
                }
            } catch (e) {
                // 跨域iframe无法访问
            }
        }

        // 检查是否在iframe中，如果是，也包含顶级文档
        if (window !== window.top) {
            try {
                docs.push(window.top.document);
            } catch (e) {
                // 跨域无法访问
            }
        }

        return docs;
    }

    // 在所有文档中查找元素
    function findElementInAllDocs(xpath) {
        var docs = getAllDocuments();

        for (var i = 0; i < docs.length; i++) {
            try {
                var element = getElementByXPath(xpath, docs[i]);
                if (element) {
                    console.log('在文档' + (i + 1) + '中找到元素');
                    return { element: element, document: docs[i] };
                }
            } catch (e) {
                // 继续查找下一个文档
            }
        }

        return null;
    }

    // 填写输入框函数
    function fillInput(inputXPath, inputValue) {
        console.log('填写输入框');
        var inputResult = findElementInAllDocs(inputXPath);

        if (inputResult) {
            var input = inputResult.element;
            input.value = inputValue;
            input.dispatchEvent(new Event('input', { bubbles: true }));
            input.dispatchEvent(new Event('change', { bubbles: true }));
            input.dispatchEvent(new Event('blur', { bubbles: true }));
            console.log('输入框填写完成，值: "' + inputValue + '"');
            return true;
        } else {
            console.log('未找到输入框: ' + inputXPath);
            return false;
        }
    }

    // 点击元素函数
    function clickElement(elementXPath, description) {
        console.log(description || '点击元素');
        var elementResult = findElementInAllDocs(elementXPath);

        if (elementResult) {
            var element = elementResult.element;
            element.click();
            console.log('点击完成: ' + (description || elementXPath));
            return true;
        } else {
            console.log('未找到元素: ' + elementXPath);
            return false;
        }
    }

    // 立即执行自动填写
    console.log('🚀 开始跨iframe自动填写...');


    // =======================执行区============================
    // 等待500ms后点击单选框
    setTimeout(function() {
        var radioXPath = "/html/body/div[9]/div[2]/div/div[2]/div/div/div/div[2]/div[6]/div[2]/div/span/span[1]";

        // 检查单选框是否已经选中
        var radioResult = findElementInAllDocs(radioXPath);
        if (radioResult) {
            var radioElement = radioResult.element;
            var className = radioElement.className;

            // 判断是否已经选中（包含 checked 相关的class）
            if (className.indexOf('checked') !== -1) {
                console.log('单选框已经选中，跳过点击');
            } else {
                console.log('单选框未选中，进行点击');
                clickElement(radioXPath, "是否跨区（默认不跨区）");
            }
        } else {
            console.log('未找到单选框元素');
        }
    }, 500);

    // 等待1000ms后第一次点击下拉框
    setTimeout(function() {
        var dropdown1XPath = "/html/body/div[9]/div[2]/div/div[2]/div/div/div/div[4]/div[6]/span/span/span[1]/span[3]";
        clickElement(dropdown1XPath, "下拉框第一次点击");
    }, 1000);

    // 等待1500ms后第二次点击下拉框
    setTimeout(function() {
        var dropdown2XPath = "html/body/div[10]/div/span[2]";
        clickElement(dropdown2XPath, "下拉框第二次点击投资类型选项为企业投资");
    }, 1500);



    // 等待2000ms后填写电子监管号1
    setTimeout(function() {
        var inputXPath = "/html/body/div[9]/div[3]/div/div[2]/div/div/div/div[1]/div[4]/span/span/span[1]/span[2]/div/input";
        fillInput(inputXPath, "/");
    }, 2000);

    // 等待2500ms后填写电子监管号2
    setTimeout(function() {
        var inputXPath = "/html/body/div[9]/div[3]/div/div[2]/div/div/div/div[1]/div[6]/span/span/span[1]/span[2]/div/input";
        fillInput(inputXPath, "/");
    }, 2500);

    // 等待3000ms后填写专门部门意见1
    setTimeout(function() {
        var inputXPath = "/html/body/div[9]/div[8]/div/div[2]/div/div/div[2]/fieldset/div/div[3]/div/div/div/div[3]/div/div[2]/div/div[2]/span[2]/span";
        fillInput(inputXPath, "核查“两重点一重大”危化品防控范围等其他专业部");
    }, 3000);

    // 等待3500ms后填写专门部门意见2
    setTimeout(function() {
        var inputXPath = "/html/body/div[9]/div[8]/div/div[2]/div/div/div[2]/fieldset/div/div[3]/div/div/div/div[3]/div/div[2]/div/div[2]/span[4]/span";
        fillInput(inputXPath, "不涉及");
    }, 3500);

    // 等待4000ms后点击保存按钮
    setTimeout(function() {
        var saveButtonXPath = "/html/body/div[3]/div/div[4]/div/div/div[2]/div/div[1]/div[1]/div[1]/button";
        clickElement(saveButtonXPath, "点击保存按钮");
        console.log('🎉 自动填写、选择和保存完成！');
    }, 4000);

})();