/**
 * 简化版XPath自动填写工具 - 兼容性更好
 */

(function() {
    console.log('🚀 XPath自动填写工具已加载');
    
    // 工具函数
    function getElementByXPath(xpath) {
        try {
            var result = document.evaluate(xpath, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null);
            return result.singleNodeValue;
        } catch (e) {
            console.error('XPath查找失败:', e);
            return null;
        }
    }
    
    function wait(ms) {
        return new Promise(function(resolve) {
            setTimeout(resolve, ms);
        });
    }
    
    // 简单的XPath生成器
    function generateSimpleXPath(element) {
        if (!element) return '';
        
        if (element.id) {
            return '//*[@id="' + element.id + '"]';
        }
        
        var path = '';
        var current = element;
        
        while (current && current.nodeType === 1) {
            var tagName = current.tagName.toLowerCase();
            var index = 1;
            
            // 计算同级同标签元素的索引
            var sibling = current.previousSibling;
            while (sibling) {
                if (sibling.nodeType === 1 && sibling.tagName === current.tagName) {
                    index++;
                }
                sibling = sibling.previousSibling;
            }
            
            path = '/' + tagName + '[' + index + ']' + path;
            current = current.parentNode;
            
            if (current === document.body) {
                path = '/html/body' + path;
                break;
            }
        }
        
        return path;
    }
    
    // 调试函数
    function debug() {
        console.log('=== 输入框信息 ===');
        var inputs = document.querySelectorAll('input');
        
        for (var i = 0; i < inputs.length; i++) {
            var inp = inputs[i];
            var xpath = generateSimpleXPath(inp);
            var rect = inp.getBoundingClientRect();
            var isVisible = inp.offsetParent !== null;
            
            console.log((i + 1) + '. Type: "' + inp.type + '", 可见: ' + isVisible);
            console.log('    XPath: ' + xpath);
            console.log('    位置: x=' + Math.round(rect.x) + ', y=' + Math.round(rect.y));
            console.log('    Class: "' + inp.className + '"');
            console.log('    ---');
        }
        
        console.log('\n=== 保存按钮信息 ===');
        var buttons = document.querySelectorAll('button, input[type="button"], input[type="submit"]');
        
        for (var i = 0; i < buttons.length; i++) {
            var btn = buttons[i];
            var text = (btn.textContent || btn.value || '').trim();
            
            if (text.indexOf('保存') !== -1) {
                var xpath = generateSimpleXPath(btn);
                console.log((i + 1) + '. Text: "' + text + '"');
                console.log('    XPath: ' + xpath);
                console.log('    Class: "' + btn.className + '"');
                console.log('    ---');
            }
        }
    }
    
    // 等待元素出现
    function waitForElement(xpath, timeout) {
        if (!timeout) timeout = 10000; // 默认10秒

        return new Promise(function(resolve, reject) {
            var startTime = Date.now();

            function checkElement() {
                var element = getElementByXPath(xpath);
                if (element) {
                    resolve(element);
                    return;
                }

                if (Date.now() - startTime > timeout) {
                    reject(new Error('超时: 未找到元素 ' + xpath));
                    return;
                }

                setTimeout(checkElement, 200);
            }

            checkElement();
        });
    }

    // 使用XPath填写输入框（带等待）
    function fillByXPath(xpath, value) {
        if (!value) value = "张三";

        console.log('🔍 使用XPath查找输入框: ' + xpath);
        console.log('⏳ 等待元素加载...');

        return waitForElement(xpath, 10000).then(function(input) {
            console.log('✅ 找到输入框');
            console.log('类型: ' + input.type + ', 当前值: "' + input.value + '"');

            // 填写输入框
            input.focus();
            input.value = '';
            input.value = value;

            // 触发事件
            input.dispatchEvent(new Event('input', { bubbles: true }));
            input.dispatchEvent(new Event('change', { bubbles: true }));
            input.dispatchEvent(new Event('blur', { bubbles: true }));

            console.log('✅ 填写完成，当前值: "' + input.value + '"');
            return true;
        }).catch(function(error) {
            console.error('❌ ' + error.message);
            console.log('💡 尝试运行 debug() 查看当前页面的元素');
            return false;
        });
    }

    // 同步版本（立即查找，不等待）
    function fillByXPathNow(xpath, value) {
        if (!value) value = "张三";

        console.log('🔍 使用XPath查找输入框: ' + xpath);

        var input = getElementByXPath(xpath);
        if (!input) {
            console.error('❌ 未找到输入框');
            return false;
        }

        console.log('✅ 找到输入框');
        console.log('类型: ' + input.type + ', 当前值: "' + input.value + '"');

        // 填写输入框
        input.focus();
        input.value = '';
        input.value = value;

        // 触发事件
        input.dispatchEvent(new Event('input', { bubbles: true }));
        input.dispatchEvent(new Event('change', { bubbles: true }));
        input.dispatchEvent(new Event('blur', { bubbles: true }));

        console.log('✅ 填写完成，当前值: "' + input.value + '"');
        return true;
    }
    
    // 使用XPath点击保存按钮
    function clickSaveByXPath(xpath) {
        console.log('🔍 使用XPath查找保存按钮: ' + xpath);
        
        var button = getElementByXPath(xpath);
        if (!button) {
            console.error('❌ 未找到保存按钮');
            return false;
        }
        
        console.log('✅ 找到保存按钮');
        console.log('文本: "' + (button.textContent || button.value) + '"');
        
        // 点击按钮
        button.scrollIntoView({ behavior: 'smooth', block: 'center' });
        button.dispatchEvent(new MouseEvent('click', {
            bubbles: true,
            cancelable: true,
            view: window
        }));
        
        console.log('✅ 保存按钮已点击');
        return true;
    }
    
    // 完整流程
    function autoFillComplete(inputXPath, saveButtonXPath, inputValue) {
        if (!inputValue) inputValue = "张三";
        
        console.log('🚀 开始完整自动填写流程...');
        
        return wait(1000).then(function() {
            // 1. 填写输入框
            var fillSuccess = fillByXPath(inputXPath, inputValue);
            if (!fillSuccess) {
                return Promise.resolve(false);
            }
            
            return wait(500);
        }).then(function() {
            // 2. 点击保存按钮
            var clickSuccess = clickSaveByXPath(saveButtonXPath);
            if (!clickSuccess) {
                return Promise.resolve(false);
            }
            
            return wait(1000);
        }).then(function() {
            // 3. 验证结果
            var input = getElementByXPath(inputXPath);
            if (input && input.value === inputValue) {
                console.log('🎉 自动填写成功！');
                return true;
            } else {
                console.log('⚠️ 值可能未保存，请检查页面状态');
                return false;
            }
        }).catch(function(error) {
            console.error('❌ 错误:', error);
            return false;
        });
    }
    
    // 使用说明
    console.log('💡 使用方法:');
    console.log('1. testYourXPath() - 测试修正后的XPath');
    console.log('2. smartFindInput() - 智能查找所有输入框');
    console.log('3. fillByXPath("XPath", "值") - 填写指定输入框');
    console.log('4. clickSaveByXPath("XPath") - 点击保存按钮');
    console.log('5. autoFillComplete("输入框XPath", "保存按钮XPath", "值") - 完整流程');
    console.log('');
    console.log('🎯 修正后的XPath: /html/body/div[3]/div[3]/div/div[2]/div/div/div/div[1]/div[4]/span/span/span[1]/span[2]/div/input');
    
    // 检查是否在iframe中
    function checkIframe() {
        if (window !== window.top) {
            console.log('⚠️ 检测到页面在iframe中');
            console.log('当前frame: ' + window.location.href);
            console.log('顶级frame: ' + window.top.location.href);
            return true;
        }
        return false;
    }

    // 测试修正后的XPath
    function testYourXPath() {
        // 修正后的XPath路径（div[9] 改为 div[3]）
        var yourXPath = "/html/body/div[3]/div[3]/div/div[2]/div/div/div/div[1]/div[4]/span/span/span[1]/span[2]/div/input";

        console.log('🧪 测试你确认的XPath...');
        console.log('XPath: ' + yourXPath);

        checkIframe();

        console.log('⏳ 等待元素加载...');

        return waitForElement(yourXPath, 15000).then(function(input) {
            console.log('🎉 成功找到你的输入框！');
            console.log('类型: ' + input.type);
            console.log('当前值: "' + input.value + '"');
            console.log('可见: ' + (input.offsetParent !== null));

            // 尝试填写
            input.focus();
            input.value = '';
            input.value = '测试值';

            input.dispatchEvent(new Event('input', { bubbles: true }));
            input.dispatchEvent(new Event('change', { bubbles: true }));

            console.log('✅ 测试填写完成，当前值: "' + input.value + '"');
            return true;
        }).catch(function(error) {
            console.error('❌ ' + error.message);
            console.log('🔍 让我们检查页面结构...');

            // 检查相似的路径
            var similarPaths = [
                "/html/body/div[3]/div[2]/div/div[2]/div/div/div/div[1]/div[4]/span/span/span[1]/span[2]/div/input",
                "/html/body/div[3]/div[4]/div/div[2]/div/div/div/div[1]/div[4]/span/span/span[1]/span[2]/div/input",
                "/html/body/div[3]/div[1]/div/div[2]/div/div/div/div[1]/div[4]/span/span/span[1]/span[2]/div/input",
                "//*[@id='app']/div[3]/div/div[2]/div/div/div/div[1]/div[4]/span/span/span[1]/span[2]/div/input"
            ];

            console.log('🔍 尝试相似的XPath路径...');
            for (var i = 0; i < similarPaths.length; i++) {
                var element = getElementByXPath(similarPaths[i]);
                if (element) {
                    console.log('✅ 找到相似路径: ' + similarPaths[i]);
                    return;
                }
            }

            console.log('💡 建议: 重新获取XPath或检查页面是否完全加载');
            return false;
        });
    }

    // 深度调试 - 分析XPath路径
    function deepDebug() {
        var yourXPath = "/html/body/div[3]/div[3]/div/div[2]/div/div/div/div[1]/div[4]/span/span/span[1]/span[2]/div/input";

        console.log('🔍 深度调试你的XPath路径...');
        console.log('目标XPath: ' + yourXPath);
        console.log('');

        // 逐级检查路径（修正后的路径）
        var pathParts = [
            "/html",
            "/html/body",
            "/html/body/div[3]",
            "/html/body/div[3]/div[3]",
            "/html/body/div[3]/div[3]/div",
            "/html/body/div[3]/div[3]/div/div[2]",
            "/html/body/div[3]/div[3]/div/div[2]/div",
            "/html/body/div[3]/div[3]/div/div[2]/div/div",
            "/html/body/div[3]/div[3]/div/div[2]/div/div/div",
            "/html/body/div[3]/div[3]/div/div[2]/div/div/div/div[1]",
            "/html/body/div[3]/div[3]/div/div[2]/div/div/div/div[1]/div[4]",
            "/html/body/div[3]/div[3]/div/div[2]/div/div/div/div[1]/div[4]/span",
            "/html/body/div[3]/div[3]/div/div[2]/div/div/div/div[1]/div[4]/span/span",
            "/html/body/div[3]/div[3]/div/div[2]/div/div/div/div[1]/div[4]/span/span/span[1]",
            "/html/body/div[3]/div[3]/div/div[2]/div/div/div/div[1]/div[4]/span/span/span[1]/span[2]",
            "/html/body/div[3]/div[3]/div/div[2]/div/div/div/div[1]/div[4]/span/span/span[1]/span[2]/div",
            "/html/body/div[3]/div[3]/div/div[2]/div/div/div/div[1]/div[4]/span/span/span[1]/span[2]/div/input"
        ];

        console.log('📍 逐级检查路径:');
        for (var i = 0; i < pathParts.length; i++) {
            var element = getElementByXPath(pathParts[i]);
            var status = element ? '✅' : '❌';
            console.log(status + ' ' + pathParts[i]);

            if (!element) {
                console.log('💥 路径在这里断开了！');

                // 检查这一级有多少个div
                var parentPath = pathParts[i - 1];
                if (parentPath) {
                    var parent = getElementByXPath(parentPath);
                    if (parent) {
                        console.log('🔍 父元素存在，检查子元素...');
                        var children = parent.children;
                        console.log('子元素数量: ' + children.length);
                        for (var j = 0; j < children.length; j++) {
                            console.log('  ' + (j + 1) + '. ' + children[j].tagName.toLowerCase() +
                                       (children[j].className ? ' class="' + children[j].className + '"' : '') +
                                       (children[j].id ? ' id="' + children[j].id + '"' : ''));
                        }
                    }
                }
                break;
            }
        }

        console.log('');
        console.log('🔍 检查页面上所有的div[9]...');
        var allDivs = document.querySelectorAll('body > div');
        console.log('body下共有 ' + allDivs.length + ' 个div:');
        for (var i = 0; i < allDivs.length; i++) {
            console.log('  div[' + (i + 1) + '] class="' + allDivs[i].className + '" id="' + allDivs[i].id + '"');
        }
    }

    // 智能查找输入框
    function smartFindInput() {
        console.log('🧠 智能查找输入框...');

        // 查找所有可能的输入框
        var allInputs = document.querySelectorAll('input[type="text"]');
        console.log('找到 ' + allInputs.length + ' 个文本输入框:');

        for (var i = 0; i < allInputs.length; i++) {
            var inp = allInputs[i];
            var xpath = generateSimpleXPath(inp);
            var rect = inp.getBoundingClientRect();
            var isVisible = inp.offsetParent !== null;

            console.log((i + 1) + '. 可见: ' + isVisible + ', 位置: (' + Math.round(rect.x) + ',' + Math.round(rect.y) + ')');
            console.log('   XPath: ' + xpath);
            console.log('   Class: "' + inp.className + '"');

            // 尝试填写这个输入框
            if (isVisible) {
                console.log('   🧪 测试填写...');
                try {
                    inp.focus();
                    var oldValue = inp.value;
                    inp.value = '测试' + (i + 1);
                    inp.dispatchEvent(new Event('input', { bubbles: true }));
                    console.log('   ✅ 填写成功: "' + inp.value + '"');
                    inp.value = oldValue; // 恢复原值
                } catch (e) {
                    console.log('   ❌ 填写失败: ' + e.message);
                }
            }
            console.log('   ---');
        }
    }

    // 导出函数
    window.debug = debug;
    window.fillByXPath = fillByXPath;
    window.fillByXPathNow = fillByXPathNow;
    window.clickSaveByXPath = clickSaveByXPath;
    window.autoFillComplete = autoFillComplete;
    window.testYourXPath = testYourXPath;
    window.waitForElement = waitForElement;
    window.deepDebug = deepDebug;
    window.smartFindInput = smartFindInput;
    
})();
