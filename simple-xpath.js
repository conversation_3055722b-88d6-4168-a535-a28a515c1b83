/**
 * 简化版XPath自动填写工具 - 基于用户验证的工作代码
 */

(function() {
    console.log('🚀 XPath自动填写工具已加载');

    // 工具函数（基于用户验证的工作代码）
    function getElementByXPath(xpath) {
        return document.evaluate(xpath, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null).singleNodeValue;
    }
    
    function wait(ms) {
        return new Promise(function(resolve) {
            setTimeout(resolve, ms);
        });
    }
    
    // 简单的XPath生成器
    function generateSimpleXPath(element) {
        if (!element) return '';
        
        if (element.id) {
            return '//*[@id="' + element.id + '"]';
        }
        
        var path = '';
        var current = element;
        
        while (current && current.nodeType === 1) {
            var tagName = current.tagName.toLowerCase();
            var index = 1;
            
            // 计算同级同标签元素的索引
            var sibling = current.previousSibling;
            while (sibling) {
                if (sibling.nodeType === 1 && sibling.tagName === current.tagName) {
                    index++;
                }
                sibling = sibling.previousSibling;
            }
            
            path = '/' + tagName + '[' + index + ']' + path;
            current = current.parentNode;
            
            if (current === document.body) {
                path = '/html/body' + path;
                break;
            }
        }
        
        return path;
    }
    
    // 调试函数
    function debug() {
        console.log('=== 输入框信息 ===');
        var inputs = document.querySelectorAll('input');
        
        for (var i = 0; i < inputs.length; i++) {
            var inp = inputs[i];
            var xpath = generateSimpleXPath(inp);
            var rect = inp.getBoundingClientRect();
            var isVisible = inp.offsetParent !== null;
            
            console.log((i + 1) + '. Type: "' + inp.type + '", 可见: ' + isVisible);
            console.log('    XPath: ' + xpath);
            console.log('    位置: x=' + Math.round(rect.x) + ', y=' + Math.round(rect.y));
            console.log('    Class: "' + inp.className + '"');
            console.log('    ---');
        }
        
        console.log('\n=== 保存按钮信息 ===');
        var buttons = document.querySelectorAll('button, input[type="button"], input[type="submit"]');
        
        for (var i = 0; i < buttons.length; i++) {
            var btn = buttons[i];
            var text = (btn.textContent || btn.value || '').trim();
            
            if (text.indexOf('保存') !== -1) {
                var xpath = generateSimpleXPath(btn);
                console.log((i + 1) + '. Text: "' + text + '"');
                console.log('    XPath: ' + xpath);
                console.log('    Class: "' + btn.className + '"');
                console.log('    ---');
            }
        }
    }
    
    // 等待元素出现并变为可操作状态
    function waitForElement(xpath, timeout) {
        if (!timeout) timeout = 15000; // 增加到15秒，因为有动态加载

        return new Promise(function(resolve, reject) {
            var startTime = Date.now();

            function checkElement() {
                // 1. 先通过XPath查找
                var element = getElementByXPath(xpath);

                // 2. 如果XPath找不到，尝试通过ID查找
                if (!element) {
                    element = document.getElementById("xui.UI.ComboInput-INPUT:ac:");
                }

                if (element) {
                    // 检查元素是否真正可操作
                    var isVisible = element.offsetParent !== null;
                    var isEnabled = !element.disabled;
                    var isInteractable = element.style.pointerEvents !== 'none';

                    console.log('🔍 找到元素，检查状态:');
                    console.log('  可见: ' + isVisible);
                    console.log('  启用: ' + isEnabled);
                    console.log('  可交互: ' + isInteractable);

                    if (isVisible && isEnabled && isInteractable) {
                        console.log('✅ 元素已准备就绪');
                        resolve(element);
                        return;
                    } else {
                        console.log('⏳ 元素存在但未准备就绪，继续等待...');
                    }
                }

                if (Date.now() - startTime > timeout) {
                    reject(new Error('超时: 元素未准备就绪 ' + xpath));
                    return;
                }

                setTimeout(checkElement, 300); // 增加检查间隔
            }

            checkElement();
        });
    }

    // 等待XHR请求完成
    function waitForXHR(timeout) {
        if (!timeout) timeout = 5000;

        return new Promise(function(resolve) {
            var startTime = Date.now();

            function checkXHR() {
                // 检查是否有活跃的XHR请求
                var hasActiveXHR = false;

                // 简单的检查方法：看是否有loading状态的元素
                var loadingElements = document.querySelectorAll('.loading, .spinner, [class*="load"]');
                hasActiveXHR = loadingElements.length > 0;

                if (!hasActiveXHR || Date.now() - startTime > timeout) {
                    console.log('✅ XHR请求似乎已完成');
                    resolve();
                    return;
                }

                console.log('⏳ 等待XHR请求完成...');
                setTimeout(checkXHR, 500);
            }

            checkXHR();
        });
    }

    // 使用XPath填写输入框（基于用户验证的工作代码）
    function fillByXPath(xpath, value) {
        if (!value) value = "张三";

        console.log('🔍 使用XPath填写输入框: ' + xpath);
        console.log('填写值: "' + value + '"');

        var input = getElementByXPath(xpath);
        if (input) {
            console.log('✅ 找到输入框');
            console.log('元素ID: ' + input.id);
            console.log('当前值: "' + input.value + '"');

            // 使用用户验证的工作代码
            input.value = value;
            input.dispatchEvent(new Event('input', { bubbles: true }));

            console.log('✅ 填写完成，当前值: "' + input.value + '"');
            return true;
        } else {
            console.error('❌ 未找到输入框');
            console.log('💡 请确保已点击按钮，输入框处于可操作状态');
            return false;
        }
    }

    // 同步版本（立即查找，不等待）
    function fillByXPathNow(xpath, value) {
        if (!value) value = "张三";

        console.log('🔍 使用XPath查找输入框: ' + xpath);

        var input = getElementByXPath(xpath);
        if (!input) {
            console.error('❌ 未找到输入框');
            return false;
        }

        console.log('✅ 找到输入框');
        console.log('类型: ' + input.type + ', 当前值: "' + input.value + '"');

        // 填写输入框
        input.focus();
        input.value = '';
        input.value = value;

        // 触发事件
        input.dispatchEvent(new Event('input', { bubbles: true }));
        input.dispatchEvent(new Event('change', { bubbles: true }));
        input.dispatchEvent(new Event('blur', { bubbles: true }));

        console.log('✅ 填写完成，当前值: "' + input.value + '"');
        return true;
    }
    
    // 使用XPath点击保存按钮
    function clickSaveByXPath(xpath) {
        console.log('🔍 使用XPath查找保存按钮: ' + xpath);

        var button = getElementByXPath(xpath);
        if (button) {
            console.log('✅ 找到保存按钮');
            console.log('按钮文本: "' + (button.textContent || button.value) + '"');

            // 点击按钮
            button.click();

            console.log('✅ 保存按钮已点击');
            return true;
        } else {
            console.error('❌ 未找到保存按钮');
            return false;
        }
    }

    // 查找保存按钮（自动查找）
    function findAndClickSave() {
        console.log('🔍 自动查找保存按钮...');

        // 查找所有包含"保存"文本的按钮
        var buttons = document.querySelectorAll('button, input[type="button"], input[type="submit"]');

        for (var i = 0; i < buttons.length; i++) {
            var btn = buttons[i];
            var text = (btn.textContent || btn.value || '').trim();

            if (text === '保存' && btn.offsetParent !== null) {
                console.log('✅ 找到保存按钮: "' + text + '"');
                btn.click();
                console.log('✅ 保存按钮已点击');
                return true;
            }
        }

        console.error('❌ 未找到保存按钮');
        return false;
    }
    
    // 完整流程（简化版）
    function autoFillComplete(inputXPath, saveButtonXPath, inputValue) {
        if (!inputValue) inputValue = "张三";

        console.log('🚀 开始完整自动填写流程...');

        // 1. 填写输入框
        var fillSuccess = fillByXPath(inputXPath, inputValue);
        if (!fillSuccess) {
            return false;
        }

        // 2. 等待一下
        setTimeout(function() {
            // 3. 点击保存按钮
            var clickSuccess;
            if (saveButtonXPath) {
                clickSuccess = clickSaveByXPath(saveButtonXPath);
            } else {
                clickSuccess = findAndClickSave();
            }

            if (clickSuccess) {
                console.log('🎉 自动填写和保存完成！');
            } else {
                console.log('⚠️ 填写成功，但保存失败');
            }
        }, 500);

        return true;
    }

    // 快速填写（使用你确认的XPath）
    function quickFill(value) {
        if (!value) value = "张三";

        console.log('🚀 快速填写: "' + value + '"');

        var yourXPath = "/html/body/div[9]/div[3]/div/div[2]/div/div/div/div[1]/div[4]/span/span/span[1]/span[2]/div/input";

        var fillSuccess = fillByXPath(yourXPath, value);
        if (fillSuccess) {
            // 自动查找并点击保存按钮
            setTimeout(function() {
                findAndClickSave();
            }, 500);
        }

        return fillSuccess;
    }
    
    // 使用说明
    console.log('💡 使用方法:');
    console.log('1. quickFill("张三") - 快速填写并保存（推荐）');
    console.log('2. fillByXPath("XPath", "值") - 填写指定输入框');
    console.log('3. findAndClickSave() - 自动查找并点击保存按钮');
    console.log('4. autoFillComplete("输入框XPath", "保存按钮XPath", "值") - 完整流程');
    console.log('');
    console.log('🎯 基于你验证的工作代码构建');
    console.log('✅ 你的XPath: /html/body/div[9]/div[3]/div/div[2]/div/div/div/div[1]/div[4]/span/span/span[1]/span[2]/div/input');
    
    // 检查是否在iframe中
    function checkIframe() {
        if (window !== window.top) {
            console.log('⚠️ 检测到页面在iframe中');
            console.log('当前frame: ' + window.location.href);
            console.log('顶级frame: ' + window.top.location.href);
            return true;
        }
        return false;
    }

    // 测试你原始的XPath并寻找替代方案
    function testYourXPath() {
        // 你原始确认的XPath
        var yourXPath = "/html/body/div[9]/div[3]/div/div[2]/div/div/div/div[1]/div[4]/span/span/span[1]/span[2]/div/input";

        console.log('🧪 测试你确认的XPath...');
        console.log('XPath: ' + yourXPath);

        checkIframe();

        console.log('⏳ 等待元素加载...');

        return waitForElement(yourXPath, 15000).then(function(input) {
            console.log('🎉 成功找到你的输入框！');
            console.log('类型: ' + input.type);
            console.log('当前值: "' + input.value + '"');
            console.log('可见: ' + (input.offsetParent !== null));

            // 尝试填写
            input.focus();
            input.value = '';
            input.value = '测试值';

            input.dispatchEvent(new Event('input', { bubbles: true }));
            input.dispatchEvent(new Event('change', { bubbles: true }));

            console.log('✅ 测试填写完成，当前值: "' + input.value + '"');
            return true;
        }).catch(function(error) {
            console.error('❌ ' + error.message);
            console.log('🔍 你的XPath找不到元素，让我们寻找替代方案...');

            // 智能搜索：寻找具有相似结构的输入框
            console.log('🧠 智能搜索相似的输入框...');

            // 搜索所有包含span嵌套结构的输入框
            var spanInputs = document.querySelectorAll('span span input[type="text"]');
            console.log('找到 ' + spanInputs.length + ' 个在span中的输入框:');

            for (var i = 0; i < spanInputs.length; i++) {
                var inp = spanInputs[i];
                var xpath = generateSimpleXPath(inp);
                var isVisible = inp.offsetParent !== null;

                console.log((i + 1) + '. 可见: ' + isVisible);
                console.log('   XPath: ' + xpath);
                console.log('   Class: "' + inp.className + '"');

                if (isVisible) {
                    console.log('   🎯 这可能是你要找的输入框！');
                    console.log('   💡 尝试: fillByXPath("' + xpath + '", "张三")');
                }
                console.log('   ---');
            }

            // 如果没找到span中的输入框，显示所有可见的文本输入框
            if (spanInputs.length === 0) {
                console.log('🔍 没找到span中的输入框，显示所有可见的文本输入框:');
                var allTextInputs = document.querySelectorAll('input[type="text"]');
                for (var j = 0; j < allTextInputs.length; j++) {
                    var inp = allTextInputs[j];
                    if (inp.offsetParent !== null) {
                        var xpath = generateSimpleXPath(inp);
                        console.log((j + 1) + '. XPath: ' + xpath);
                        console.log('   Class: "' + inp.className + '"');
                        console.log('   💡 尝试: fillByXPath("' + xpath + '", "张三")');
                        console.log('   ---');
                    }
                }
            }

            return false;
        });
    }

    // 深度调试 - 分析XPath路径
    function deepDebug() {
        var yourXPath = "/html/body/div[9]/div[3]/div/div[2]/div/div/div/div[1]/div[4]/span/span/span[1]/span[2]/div/input";

        console.log('🔍 深度调试你的XPath路径...');
        console.log('目标XPath: ' + yourXPath);
        console.log('');

        // 逐级检查路径（你原始的XPath）
        var pathParts = [
            "/html",
            "/html/body",
            "/html/body/div[9]",
            "/html/body/div[9]/div[3]",
            "/html/body/div[9]/div[3]/div",
            "/html/body/div[9]/div[3]/div/div[2]",
            "/html/body/div[9]/div[3]/div/div[2]/div",
            "/html/body/div[9]/div[3]/div/div[2]/div/div",
            "/html/body/div[9]/div[3]/div/div[2]/div/div/div",
            "/html/body/div[9]/div[3]/div/div[2]/div/div/div/div[1]",
            "/html/body/div[9]/div[3]/div/div[2]/div/div/div/div[1]/div[4]",
            "/html/body/div[9]/div[3]/div/div[2]/div/div/div/div[1]/div[4]/span",
            "/html/body/div[9]/div[3]/div/div[2]/div/div/div/div[1]/div[4]/span/span",
            "/html/body/div[9]/div[3]/div/div[2]/div/div/div/div[1]/div[4]/span/span/span[1]",
            "/html/body/div[9]/div[3]/div/div[2]/div/div/div/div[1]/div[4]/span/span/span[1]/span[2]",
            "/html/body/div[9]/div[3]/div/div[2]/div/div/div/div[1]/div[4]/span/span/span[1]/span[2]/div",
            "/html/body/div[9]/div[3]/div/div[2]/div/div/div/div[1]/div[4]/span/span/span[1]/span[2]/div/input"
        ];

        console.log('📍 逐级检查路径:');
        for (var i = 0; i < pathParts.length; i++) {
            var element = getElementByXPath(pathParts[i]);
            var status = element ? '✅' : '❌';
            console.log(status + ' ' + pathParts[i]);

            if (!element) {
                console.log('💥 路径在这里断开了！');

                // 检查这一级有多少个div
                var parentPath = pathParts[i - 1];
                if (parentPath) {
                    var parent = getElementByXPath(parentPath);
                    if (parent) {
                        console.log('🔍 父元素存在，检查子元素...');
                        var children = parent.children;
                        console.log('子元素数量: ' + children.length);
                        for (var j = 0; j < children.length; j++) {
                            console.log('  ' + (j + 1) + '. ' + children[j].tagName.toLowerCase() +
                                       (children[j].className ? ' class="' + children[j].className + '"' : '') +
                                       (children[j].id ? ' id="' + children[j].id + '"' : ''));
                        }
                    }
                }
                break;
            }
        }

        console.log('');
        console.log('🔍 检查页面上所有的div[9]...');
        var allDivs = document.querySelectorAll('body > div');
        console.log('body下共有 ' + allDivs.length + ' 个div:');
        for (var i = 0; i < allDivs.length; i++) {
            console.log('  div[' + (i + 1) + '] class="' + allDivs[i].className + '" id="' + allDivs[i].id + '"');
        }
    }

    // 智能查找输入框
    function smartFindInput() {
        console.log('🧠 智能查找输入框...');

        // 查找所有可能的输入框
        var allInputs = document.querySelectorAll('input[type="text"]');
        console.log('找到 ' + allInputs.length + ' 个文本输入框:');

        for (var i = 0; i < allInputs.length; i++) {
            var inp = allInputs[i];
            var xpath = generateSimpleXPath(inp);
            var rect = inp.getBoundingClientRect();
            var isVisible = inp.offsetParent !== null;

            console.log((i + 1) + '. 可见: ' + isVisible + ', 位置: (' + Math.round(rect.x) + ',' + Math.round(rect.y) + ')');
            console.log('   XPath: ' + xpath);
            console.log('   Class: "' + inp.className + '"');

            // 尝试填写这个输入框
            if (isVisible) {
                console.log('   🧪 测试填写...');
                try {
                    inp.focus();
                    var oldValue = inp.value;
                    inp.value = '测试' + (i + 1);
                    inp.dispatchEvent(new Event('input', { bubbles: true }));
                    console.log('   ✅ 填写成功: "' + inp.value + '"');
                    inp.value = oldValue; // 恢复原值
                } catch (e) {
                    console.log('   ❌ 填写失败: ' + e.message);
                }
            }
            console.log('   ---');
        }
    }

    // 导出函数
    window.debug = debug;
    window.fillByXPath = fillByXPath;
    window.fillByXPathNow = fillByXPathNow;
    window.clickSaveByXPath = clickSaveByXPath;
    window.autoFillComplete = autoFillComplete;
    window.testYourXPath = testYourXPath;
    window.waitForElement = waitForElement;
    window.deepDebug = deepDebug;
    window.smartFindInput = smartFindInput;
    
})();
