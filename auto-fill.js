/**
 * 自动填写表单工具
 * 使用完整XPath定位元素
 */

(function() {
    console.log('🚀 开始自动填写...');
    
    // 输入框的完整XPath
    const inputXPath = "/html/body/div[9]/div[3]/div/div[2]/div/div/div/div[1]/div[4]/span/span/span[1]/span[2]/div/input";
    
    // 工具函数
    function getElementByXPath(xpath) {
        const result = document.evaluate(xpath, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null);
        return result.singleNodeValue;
    }
    
    function wait(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    
    // 调试函数 - 显示页面元素
    function debug() {
        console.log('=== 调试信息 ===');
        console.log('所有输入框:');
        document.querySelectorAll('input').forEach((inp, i) => {
            console.log(`${i+1}. ID: "${inp.id}", Type: "${inp.type}", Class: "${inp.className}"`);
        });
        
        console.log('\n所有按钮:');
        document.querySelectorAll('button, input[type="button"], input[type="submit"]').forEach((btn, i) => {
            const text = (btn.textContent || btn.value || '').trim();
            console.log(`${i+1}. Text: "${text}", ID: "${btn.id}", Class: "${btn.className}"`);
        });
    }
    
    // 查找保存按钮
    function findSaveButton() {
        const buttons = document.querySelectorAll('button, input[type="button"], input[type="submit"], a, span, div');
        
        for (const btn of buttons) {
            const text = (btn.textContent || btn.value || btn.innerText || '').trim();
            if (text.includes('保存') && btn.offsetParent !== null) {
                return btn;
            }
        }
        return null;
    }
    
    // 主要执行函数
    async function execute(inputValue = "张三") {
        try {
            // 等待页面加载
            await wait(1000);
            
            // 1. 查找输入框
            console.log('🔍 查找输入框...');
            const input = getElementByXPath(inputXPath);
            
            if (!input) {
                console.error('❌ 未找到输入框');
                console.log('💡 运行 debug() 查看页面元素');
                return false;
            }
            
            console.log('✅ 找到输入框');
            
            // 2. 填写输入框
            console.log(`✏️ 填写值: "${inputValue}"`);
            input.focus();
            input.value = '';
            input.value = inputValue;
            
            // 触发事件
            const events = ['input', 'change', 'keyup', 'blur'];
            for (const eventType of events) {
                input.dispatchEvent(new Event(eventType, { bubbles: true }));
                await wait(50);
            }
            
            console.log('✅ 输入完成，当前值:', input.value);
            await wait(500);
            
            // 3. 查找并点击保存按钮
            console.log('🔍 查找保存按钮...');
            const saveBtn = findSaveButton();
            
            if (!saveBtn) {
                console.error('❌ 未找到保存按钮');
                console.log('💡 运行 debug() 查看页面元素');
                return false;
            }
            
            console.log('✅ 找到保存按钮');
            
            // 4. 点击保存按钮
            console.log('🖱️ 点击保存按钮...');
            saveBtn.scrollIntoView({ behavior: 'smooth', block: 'center' });
            await wait(300);
            
            saveBtn.dispatchEvent(new MouseEvent('click', { 
                bubbles: true, 
                cancelable: true, 
                view: window 
            }));
            
            console.log('✅ 保存按钮已点击');
            await wait(1000);
            
            // 5. 验证结果
            const finalValue = input.value;
            if (finalValue === inputValue) {
                console.log('🎉 自动填写成功！');
                return true;
            } else {
                console.log('⚠️ 值可能未保存，请检查页面状态');
                return false;
            }
            
        } catch (error) {
            console.error('❌ 错误:', error);
            return false;
        }
    }
    
    // 立即执行
    execute("张三").then(success => {
        if (success) {
            console.log('🎊 任务完成！');
        } else {
            console.log('🔧 如果失败，请运行: debug()');
        }
    });
    
    // 导出函数供手动调用
    window.autoFill = execute;
    window.debug = debug;
    
})();
