/**
 * 自动填写表单工具
 * 适用于 yth.gzlpc.gov.cn 系统
 */

class AutoFillTool {
    constructor() {
        this.maxRetries = 10;
        this.retryDelay = 500; // 500ms
        this.debug = true;
    }

    log(message) {
        if (this.debug) {
            console.log(`[AutoFill] ${message}`);
        }
    }

    // 等待元素出现
    async waitForElement(selector, timeout = 10000) {
        return new Promise((resolve, reject) => {
            const startTime = Date.now();
            
            const checkElement = () => {
                const element = document.querySelector(selector);
                if (element) {
                    this.log(`找到元素: ${selector}`);
                    resolve(element);
                    return;
                }
                
                if (Date.now() - startTime > timeout) {
                    reject(new Error(`超时: 未找到元素 ${selector}`));
                    return;
                }
                
                setTimeout(checkElement, 100);
            };
            
            checkElement();
        });
    }

    // 使用XPath查找元素
    findElementByXPath(xpath) {
        const result = document.evaluate(
            xpath,
            document,
            null,
            XPathResult.FIRST_ORDERED_NODE_TYPE,
            null
        );
        return result.singleNodeValue;
    }

    // 等待XPath元素出现
    async waitForElementByXPath(xpath, timeout = 10000) {
        return new Promise((resolve, reject) => {
            const startTime = Date.now();
            
            const checkElement = () => {
                const element = this.findElementByXPath(xpath);
                if (element) {
                    this.log(`通过XPath找到元素: ${xpath}`);
                    resolve(element);
                    return;
                }
                
                if (Date.now() - startTime > timeout) {
                    reject(new Error(`超时: 未通过XPath找到元素 ${xpath}`));
                    return;
                }
                
                setTimeout(checkElement, 100);
            };
            
            checkElement();
        });
    }

    // 模拟真实用户输入
    async simulateUserInput(element, value) {
        // 聚焦元素
        element.focus();
        
        // 清空现有内容
        element.value = '';
        
        // 触发焦点事件
        element.dispatchEvent(new FocusEvent('focus', { bubbles: true }));
        
        // 逐字符输入（模拟真实打字）
        for (let i = 0; i < value.length; i++) {
            const char = value[i];
            
            // 键盘按下事件
            element.dispatchEvent(new KeyboardEvent('keydown', {
                key: char,
                code: `Key${char.toUpperCase()}`,
                bubbles: true,
                cancelable: true
            }));
            
            // 更新值
            element.value += char;
            
            // 输入事件
            element.dispatchEvent(new InputEvent('input', {
                data: char,
                bubbles: true,
                cancelable: true
            }));
            
            // 键盘释放事件
            element.dispatchEvent(new KeyboardEvent('keyup', {
                key: char,
                code: `Key${char.toUpperCase()}`,
                bubbles: true,
                cancelable: true
            }));
            
            // 小延迟模拟真实打字速度
            await new Promise(resolve => setTimeout(resolve, 50));
        }
        
        // 触发change和blur事件
        element.dispatchEvent(new Event('change', { bubbles: true }));
        element.dispatchEvent(new FocusEvent('blur', { bubbles: true }));
        
        this.log(`已输入值: ${value}`);
    }

    // 查找输入框的多种方法
    async findInputElement() {
        // 首先使用确认的正确XPath
        const correctXPath = "//*[@id=\"xui.UI.ComboInput-INPUT:ac:\"]";

        try {
            this.log(`尝试使用确认的XPath: ${correctXPath}`);
            const element = await this.waitForElementByXPath(correctXPath, 5000);
            if (element) {
                this.log('✅ 使用确认的XPath找到输入框');
                return element;
            }
        } catch (e) {
            this.log(`❌ 确认的XPath失败: ${e.message}`);
        }

        // 备用CSS选择器
        const selectors = [
            "input#xui\\.UI\\.ComboInput-INPUT\\:ac\\:",
            "input[id='xui.UI.ComboInput-INPUT:ac:']",
            "input[id*='ComboInput-INPUT']",
            "input[id*='ac']",
            "input[type='text']",
            "input[class*='combo']",
            "input[class*='input']"
        ];

        // 备用XPath
        const xpaths = [
            "//input[@id='xui.UI.ComboInput-INPUT:ac:']",
            "//input[contains(@id, 'ComboInput-INPUT')]",
            "//input[contains(@id, 'ac')]",
            "//input[@type='text']",
            "//input[contains(@class, 'combo')]",
            "//input[contains(@class, 'input')]"
        ];

        // 尝试CSS选择器
        for (const selector of selectors) {
            try {
                const element = await this.waitForElement(selector, 2000);
                if (element) {
                    this.log(`✅ 使用CSS选择器找到输入框: ${selector}`);
                    return element;
                }
            } catch (e) {
                this.log(`CSS选择器失败: ${selector}`);
            }
        }

        // 尝试备用XPath
        for (const xpath of xpaths) {
            try {
                const element = await this.waitForElementByXPath(xpath, 2000);
                if (element) {
                    this.log(`✅ 使用备用XPath找到输入框: ${xpath}`);
                    return element;
                }
            } catch (e) {
                this.log(`XPath失败: ${xpath}`);
            }
        }

        throw new Error('未找到任何输入框');
    }

    // 查找保存按钮的多种方法
    async findSaveButton() {
        const selectors = [
            "button:contains('保存')",
            "input[type='button'][value*='保存']",
            "input[type='submit'][value*='保存']",
            "a:contains('保存')",
            "[onclick*='save']",
            "[onclick*='保存']"
        ];

        const xpaths = [
            "//button[contains(text(), '保存')]",
            "//input[@type='button' and contains(@value, '保存')]",
            "//input[@type='submit' and contains(@value, '保存')]",
            "//a[contains(text(), '保存')]",
            "//*[contains(@onclick, 'save')]",
            "//*[contains(@onclick, '保存')]",
            "//button[contains(@class, 'save')]",
            "//*[@id='saveBtn' or @id='save' or @id='btnSave']"
        ];

        // 先尝试通过文本内容查找
        const buttons = Array.from(document.querySelectorAll('button, input[type="button"], input[type="submit"], a'));
        for (const btn of buttons) {
            if (btn.textContent && btn.textContent.includes('保存')) {
                this.log('通过文本内容找到保存按钮');
                return btn;
            }
            if (btn.value && btn.value.includes('保存')) {
                this.log('通过value属性找到保存按钮');
                return btn;
            }
        }

        // 再尝试XPath
        for (const xpath of xpaths) {
            try {
                const element = await this.waitForElementByXPath(xpath, 1000);
                if (element) return element;
            } catch (e) {
                this.log(`保存按钮XPath失败: ${xpath}`);
            }
        }

        throw new Error('未找到保存按钮');
    }

    // 模拟真实点击
    async simulateClick(element) {
        // 确保元素可见和可点击
        element.scrollIntoView({ behavior: 'smooth', block: 'center' });
        
        // 等待滚动完成
        await new Promise(resolve => setTimeout(resolve, 300));
        
        // 触发鼠标事件序列
        element.dispatchEvent(new MouseEvent('mousedown', {
            bubbles: true,
            cancelable: true,
            view: window,
            button: 0
        }));
        
        await new Promise(resolve => setTimeout(resolve, 50));
        
        element.dispatchEvent(new MouseEvent('mouseup', {
            bubbles: true,
            cancelable: true,
            view: window,
            button: 0
        }));
        
        element.dispatchEvent(new MouseEvent('click', {
            bubbles: true,
            cancelable: true,
            view: window,
            button: 0
        }));
        
        this.log('已触发点击事件');
    }

    // 主要执行函数
    async execute(inputValue = "张三") {
        try {
            this.log('开始自动填写...');
            
            // 等待页面完全加载
            if (document.readyState !== 'complete') {
                await new Promise(resolve => {
                    window.addEventListener('load', resolve);
                });
            }
            
            // 额外等待，确保动态内容加载完成
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            // 查找并填写输入框
            this.log('正在查找输入框...');
            const inputElement = await this.findInputElement();
            
            this.log('正在填写输入框...');
            await this.simulateUserInput(inputElement, inputValue);
            
            // 等待一下，让输入生效
            await new Promise(resolve => setTimeout(resolve, 500));
            
            // 查找并点击保存按钮
            this.log('正在查找保存按钮...');
            const saveButton = await this.findSaveButton();
            
            this.log('正在点击保存按钮...');
            await this.simulateClick(saveButton);
            
            this.log('自动填写完成！');
            return true;
            
        } catch (error) {
            this.log(`错误: ${error.message}`);
            console.error(error);
            return false;
        }
    }

    // 调试模式：显示页面上所有可能的输入框和按钮
    debugElements() {
        console.log('=== 调试信息 ===');
        
        console.log('所有输入框:');
        const inputs = document.querySelectorAll('input');
        inputs.forEach((input, index) => {
            console.log(`${index + 1}. ID: ${input.id}, Class: ${input.className}, Type: ${input.type}, Name: ${input.name}`);
        });
        
        console.log('所有按钮:');
        const buttons = document.querySelectorAll('button, input[type="button"], input[type="submit"]');
        buttons.forEach((btn, index) => {
            console.log(`${index + 1}. Text: ${btn.textContent || btn.value}, ID: ${btn.id}, Class: ${btn.className}`);
        });
    }
}

// 创建实例并导出
const autoFill = new AutoFillTool();

// 立即执行函数
(async function() {
    // 如果需要调试，取消下面这行的注释
    // autoFill.debugElements();
    
    const success = await autoFill.execute("张三");
    if (success) {
        console.log("✅ 自动填写成功！");
    } else {
        console.log("❌ 自动填写失败，请检查页面元素");
        console.log("💡 运行 autoFill.debugElements() 查看页面元素信息");
    }
})();

// 导出供手动调用
window.autoFill = autoFill;
