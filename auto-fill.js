/**
 * 自动填写表单工具 - 基于XPath精确定位
 */

(function() {
    console.log('🚀 开始自动填写...');

    // 工具函数
    function getElementByXPath(xpath) {
        try {
            const result = document.evaluate(xpath, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null);
            return result.singleNodeValue;
        } catch (e) {
            console.error('XPath查找失败:', e);
            return null;
        }
    }

    function wait(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    
    // 调试函数 - 显示页面元素
    function debug() {
        console.log('=== 调试信息 ===');
        console.log('所有输入框:');
        document.querySelectorAll('input').forEach((inp, i) => {
            // 获取输入框的位置和上下文信息
            const rect = inp.getBoundingClientRect();
            const parent = inp.parentElement;
            const label = findLabelForInput(inp);

            console.log(`${i+1}. Type: "${inp.type}", Class: "${inp.className}"`);
            console.log(`    位置: x=${Math.round(rect.x)}, y=${Math.round(rect.y)}, 可见: ${inp.offsetParent !== null}`);
            console.log(`    标签: "${label}", 占位符: "${inp.placeholder}"`);
            console.log(`    父元素: ${parent.tagName}.${parent.className}`);
            console.log('    ---');
        });

        console.log('\n所有按钮:');
        document.querySelectorAll('button, input[type="button"], input[type="submit"]').forEach((btn, i) => {
            const text = (btn.textContent || btn.value || '').trim();
            console.log(`${i+1}. Text: "${text}", ID: "${btn.id}", Class: "${btn.className}"`);
        });
    }

    // 查找输入框的标签文本
    function findLabelForInput(input) {
        // 查找关联的label
        if (input.id) {
            const label = document.querySelector(`label[for="${input.id}"]`);
            if (label) return label.textContent.trim();
        }

        // 查找父元素中的文本
        let parent = input.parentElement;
        for (let i = 0; i < 3 && parent; i++) {
            const text = parent.textContent.trim();
            if (text && text.length < 50) {
                // 移除输入框本身的值
                const cleanText = text.replace(input.value || '', '').trim();
                if (cleanText && cleanText !== text) {
                    return cleanText;
                }
            }
            parent = parent.parentElement;
        }

        // 查找前面的兄弟元素
        let sibling = input.previousElementSibling;
        while (sibling) {
            const text = sibling.textContent.trim();
            if (text && text.length < 30) {
                return text;
            }
            sibling = sibling.previousElementSibling;
        }

        return '未找到标签';
    }
    
    // 查找保存按钮
    function findSaveButton() {
        const buttons = document.querySelectorAll('button, input[type="button"], input[type="submit"]');

        // 查找第一个包含"保存"文本的按钮
        for (const btn of buttons) {
            const text = (btn.textContent || btn.value || btn.innerText || '').trim();
            if (text === '保存' && btn.offsetParent !== null) {
                console.log('✅ 找到保存按钮:', {
                    text: text,
                    className: btn.className
                });
                return btn;
            }
        }
        return null;
    }
    
    // 查找输入框的多种方法
    function findInputElement() {
        // 1. 先尝试原始XPath
        let input = getElementByXPath(inputXPath);
        if (input) {
            console.log('✅ 通过原始XPath找到输入框');
            return input;
        }

        // 2. 查找所有text类型的输入框，选择第一个可见的
        const textInputs = document.querySelectorAll('input[type="text"]');
        for (const inp of textInputs) {
            if (inp.offsetParent !== null) { // 确保可见
                console.log('✅ 找到第一个可见的text输入框');
                return inp;
            }
        }

        // 3. 查找包含特定class的输入框
        const classInputs = document.querySelectorAll('input.ng-pristine, input.form-control');
        for (const inp of classInputs) {
            if (inp.type === 'text' && inp.offsetParent !== null) {
                console.log('✅ 通过class找到输入框');
                return inp;
            }
        }

        return null;
    }

    // 主要执行函数
    async function execute(inputValue = "张三") {
        try {
            // 等待页面加载
            await wait(1000);

            // 1. 查找输入框
            console.log('🔍 查找输入框...');
            const input = findInputElement();

            if (!input) {
                console.error('❌ 未找到输入框');
                console.log('💡 运行 debug() 查看页面元素');
                return false;
            }

            console.log('✅ 找到输入框:', {
                type: input.type,
                className: input.className,
                value: input.value
            });
            
            // 2. 填写输入框
            console.log(`✏️ 填写值: "${inputValue}"`);
            input.focus();
            input.value = '';
            input.value = inputValue;
            
            // 触发事件
            const events = ['input', 'change', 'keyup', 'blur'];
            for (const eventType of events) {
                input.dispatchEvent(new Event(eventType, { bubbles: true }));
                await wait(50);
            }
            
            console.log('✅ 输入完成，当前值:', input.value);
            await wait(500);
            
            // 3. 查找并点击保存按钮
            console.log('🔍 查找保存按钮...');
            const saveBtn = findSaveButton();
            
            if (!saveBtn) {
                console.error('❌ 未找到保存按钮');
                console.log('💡 运行 debug() 查看页面元素');
                return false;
            }
            
            console.log('✅ 找到保存按钮');
            
            // 4. 点击保存按钮
            console.log('🖱️ 点击保存按钮...');
            saveBtn.scrollIntoView({ behavior: 'smooth', block: 'center' });
            await wait(300);
            
            saveBtn.dispatchEvent(new MouseEvent('click', { 
                bubbles: true, 
                cancelable: true, 
                view: window 
            }));
            
            console.log('✅ 保存按钮已点击');
            await wait(1000);
            
            // 5. 验证结果
            const finalValue = input.value;
            if (finalValue === inputValue) {
                console.log('🎉 自动填写成功！');
                return true;
            } else {
                console.log('⚠️ 值可能未保存，请检查页面状态');
                return false;
            }
            
        } catch (error) {
            console.error('❌ 错误:', error);
            return false;
        }
    }
    
    // 立即执行
    execute("张三").then(success => {
        if (success) {
            console.log('🎊 任务完成！');
        } else {
            console.log('🔧 如果失败，请运行: debug()');
        }
    });
    
    // 手动选择输入框进行填写
    function fillSpecificInput(index, value = "张三") {
        const inputs = document.querySelectorAll('input');
        if (index < 1 || index > inputs.length) {
            console.error(`❌ 输入框索引无效，请选择 1-${inputs.length}`);
            return false;
        }

        const input = inputs[index - 1];
        if (input.type !== 'text') {
            console.error(`❌ 第${index}个输入框不是文本类型，类型为: ${input.type}`);
            return false;
        }

        console.log(`✏️ 填写第${index}个输入框...`);
        console.log(`输入框信息: 类型=${input.type}, 标签="${findLabelForInput(input)}"`);

        input.focus();
        input.value = '';
        input.value = value;

        // 触发事件
        input.dispatchEvent(new Event('input', { bubbles: true }));
        input.dispatchEvent(new Event('change', { bubbles: true }));
        input.dispatchEvent(new Event('blur', { bubbles: true }));

        console.log('✅ 填写完成，当前值:', input.value);
        return true;
    }

    // 根据标签文本查找并填写输入框
    function fillByLabel(labelText, value = "张三") {
        const inputs = document.querySelectorAll('input[type="text"]');

        for (const input of inputs) {
            const label = findLabelForInput(input);
            if (label.includes(labelText)) {
                console.log(`✅ 找到标签包含"${labelText}"的输入框: "${label}"`);

                input.focus();
                input.value = '';
                input.value = value;

                // 触发事件
                input.dispatchEvent(new Event('input', { bubbles: true }));
                input.dispatchEvent(new Event('change', { bubbles: true }));
                input.dispatchEvent(new Event('blur', { bubbles: true }));

                console.log('✅ 填写完成，当前值:', input.value);
                return true;
            }
        }

        console.error(`❌ 未找到标签包含"${labelText}"的输入框`);
        return false;
    }

    // 根据位置查找输入框（按照页面从上到下的顺序）
    function fillByPosition(position, value = "张三") {
        const inputs = Array.from(document.querySelectorAll('input[type="text"]'))
            .filter(inp => inp.offsetParent !== null) // 只要可见的
            .sort((a, b) => {
                const rectA = a.getBoundingClientRect();
                const rectB = b.getBoundingClientRect();
                return rectA.y - rectB.y; // 按Y坐标排序
            });

        if (position < 1 || position > inputs.length) {
            console.error(`❌ 位置无效，页面上共有 ${inputs.length} 个可见的文本输入框`);
            return false;
        }

        const input = inputs[position - 1];
        const label = findLabelForInput(input);

        console.log(`✏️ 填写第${position}个可见输入框 (标签: "${label}")...`);

        input.focus();
        input.value = '';
        input.value = value;

        // 触发事件
        input.dispatchEvent(new Event('input', { bubbles: true }));
        input.dispatchEvent(new Event('change', { bubbles: true }));
        input.dispatchEvent(new Event('blur', { bubbles: true }));

        console.log('✅ 填写完成，当前值:', input.value);
        return true;
    }

    // 导出函数供手动调用
    window.autoFill = execute;
    window.debug = debug;
    window.fillInput = fillSpecificInput;
    window.fillByLabel = fillByLabel;
    window.fillByPosition = fillByPosition;
    
})();
