/**
 * 简化版自动填写工具
 * 使用完整XPath定位输入框
 */

// 调试函数：显示页面上所有元素
function debugPageElements() {
    console.log('=== 页面元素调试信息 ===');
    
    console.log('所有输入框:');
    const inputs = document.querySelectorAll('input');
    inputs.forEach((input, index) => {
        console.log(`${index + 1}. ID: "${input.id}", Class: "${input.className}", Type: "${input.type}", Name: "${input.name}", Placeholder: "${input.placeholder}"`);
    });
    
    console.log('\n所有按钮:');
    const buttons = document.querySelectorAll('button, input[type="button"], input[type="submit"], a');
    buttons.forEach((btn, index) => {
        const text = btn.textContent || btn.value || btn.innerText || '';
        console.log(`${index + 1}. Text: "${text.trim()}", ID: "${btn.id}", Class: "${btn.className}", Tag: "${btn.tagName}"`);
    });
    
    console.log('\n所有包含"保存"的元素:');
    const saveElements = document.querySelectorAll('*');
    Array.from(saveElements).forEach((el, index) => {
        const text = el.textContent || el.value || '';
        if (text.includes('保存')) {
            console.log(`${index + 1}. Text: "${text.trim()}", Tag: "${el.tagName}", ID: "${el.id}", Class: "${el.className}"`);
        }
    });
}

// 等待函数
function wait(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

// 查找输入框的函数
function findInputElement() {
    // 首先使用确认的完整XPath
    const correctXPath = "/html/body/div[9]/div[3]/div/div[2]/div/div/div/div[1]/div[4]/span/span/span[1]/span[2]/div/input";
    
    try {
        console.log(`尝试使用确认的完整XPath: ${correctXPath}`);
        const result = document.evaluate(correctXPath, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null);
        const element = result.singleNodeValue;
        if (element && element.offsetParent !== null) {
            console.log('✅ 使用确认的完整XPath找到输入框');
            return element;
        }
    } catch (e) {
        console.log(`❌ 确认的完整XPath失败:`, e);
    }
    
    // 备用CSS选择器
    const selectors = [
        "input[id='xui.UI.ComboInput-INPUT:ac:']",
        "input#xui\\.UI\\.ComboInput-INPUT\\:ac\\:",
        "input[id*='ComboInput-INPUT']",
        "input[id*='ac']",
        "input[type='text']",
        "input[class*='combo']",
        "input[class*='input']",
        "input:not([type='hidden']):not([type='button']):not([type='submit'])"
    ];
    
    for (const selector of selectors) {
        try {
            const element = document.querySelector(selector);
            if (element && element.offsetParent !== null) { // 确保元素可见
                console.log(`✅ 找到输入框，使用选择器: ${selector}`);
                return element;
            }
        } catch (e) {
            console.log(`选择器失败: ${selector}`, e);
        }
    }
    
    // 备用XPath
    const xpaths = [
        "//*[@id='xui.UI.ComboInput-INPUT:ac:']",
        "//input[@id='xui.UI.ComboInput-INPUT:ac:']",
        "//input[contains(@id, 'ComboInput-INPUT')]",
        "//input[contains(@id, 'ac')]",
        "//input[@type='text' and not(@style='display: none')]"
    ];
    
    for (const xpath of xpaths) {
        try {
            const result = document.evaluate(xpath, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null);
            const element = result.singleNodeValue;
            if (element && element.offsetParent !== null) {
                console.log(`✅ 通过备用XPath找到输入框: ${xpath}`);
                return element;
            }
        } catch (e) {
            console.log(`XPath失败: ${xpath}`, e);
        }
    }
    
    return null;
}

// 查找保存按钮的函数
function findSaveButton() {
    // 先通过文本内容查找
    const allButtons = document.querySelectorAll('button, input[type="button"], input[type="submit"], a, span, div');
    
    for (const btn of allButtons) {
        const text = (btn.textContent || btn.value || btn.innerText || '').trim();
        if (text.includes('保存') && btn.offsetParent !== null) {
            console.log(`找到保存按钮，文本: "${text}"`);
            return btn;
        }
    }
    
    // 通过XPath查找
    const xpaths = [
        "//button[contains(text(), '保存')]",
        "//input[@type='button' and contains(@value, '保存')]",
        "//input[@type='submit' and contains(@value, '保存')]",
        "//*[contains(text(), '保存') and (@onclick or @click)]"
    ];
    
    for (const xpath of xpaths) {
        try {
            const result = document.evaluate(xpath, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null);
            const element = result.singleNodeValue;
            if (element && element.offsetParent !== null) {
                console.log(`通过XPath找到保存按钮: ${xpath}`);
                return element;
            }
        } catch (e) {
            console.log(`保存按钮XPath失败: ${xpath}`, e);
        }
    }
    
    return null;
}

// 填写输入框
async function fillInput(element, value) {
    try {
        // 聚焦
        element.focus();
        
        // 清空
        element.value = '';
        
        // 设置值
        element.value = value;
        
        // 触发事件
        const events = ['input', 'change', 'keyup', 'blur'];
        for (const eventType of events) {
            const event = new Event(eventType, { bubbles: true, cancelable: true });
            element.dispatchEvent(event);
            await wait(50);
        }
        
        console.log(`已填写值: ${value}`);
        return true;
    } catch (e) {
        console.error('填写输入框失败:', e);
        return false;
    }
}

// 点击按钮
async function clickButton(element) {
    try {
        // 滚动到元素位置
        element.scrollIntoView({ behavior: 'smooth', block: 'center' });
        await wait(300);
        
        // 触发点击事件
        const events = ['mousedown', 'mouseup', 'click'];
        for (const eventType of events) {
            const event = new MouseEvent(eventType, {
                bubbles: true,
                cancelable: true,
                view: window
            });
            element.dispatchEvent(event);
            await wait(50);
        }
        
        console.log('已点击保存按钮');
        return true;
    } catch (e) {
        console.error('点击按钮失败:', e);
        return false;
    }
}

// 主执行函数
async function executeAutoFill(inputValue = "张三") {
    console.log('开始执行自动填写...');
    
    try {
        // 等待页面加载
        await wait(1000);
        
        // 查找输入框
        console.log('正在查找输入框...');
        const inputElement = findInputElement();
        
        if (!inputElement) {
            console.error('❌ 未找到输入框');
            console.log('💡 运行 debugPageElements() 查看页面所有元素');
            return false;
        }
        
        // 填写输入框
        console.log('正在填写输入框...');
        const fillSuccess = await fillInput(inputElement, inputValue);
        
        if (!fillSuccess) {
            console.error('❌ 填写输入框失败');
            return false;
        }
        
        // 等待一下
        await wait(500);
        
        // 查找保存按钮
        console.log('正在查找保存按钮...');
        const saveButton = findSaveButton();
        
        if (!saveButton) {
            console.error('❌ 未找到保存按钮');
            console.log('💡 运行 debugPageElements() 查看页面所有元素');
            return false;
        }
        
        // 点击保存按钮
        console.log('正在点击保存按钮...');
        const clickSuccess = await clickButton(saveButton);
        
        if (!clickSuccess) {
            console.error('❌ 点击保存按钮失败');
            return false;
        }
        
        console.log('✅ 自动填写完成！');
        return true;
        
    } catch (error) {
        console.error('❌ 执行过程中出错:', error);
        return false;
    }
}

// 立即执行
(async function() {
    const success = await executeAutoFill("张三");
    if (!success) {
        console.log('\n🔍 如果失败，请运行以下命令查看页面元素:');
        console.log('debugPageElements()');
        console.log('\n🔄 或者手动执行:');
        console.log('executeAutoFill("你的名字")');
    }
})();

// 导出函数供手动调用
window.debugPageElements = debugPageElements;
window.executeAutoFill = executeAutoFill;
